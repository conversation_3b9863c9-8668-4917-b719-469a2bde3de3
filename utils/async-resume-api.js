/**
 * 异步简历解析API工具
 * 使用新的异步架构，避免超时问题
 */

class AsyncResumeAPI {
  constructor() {
    this.pollingInterval = 15000; // 15秒轮询一次（用户要求）
    this.maxPollingTime = 180000; // 最大轮询3分钟
  }

  /**
   * 提交简历解析任务
   * @param {Object} params - 参数对象
   * @param {string} params.resumeContent - 简历内容
   * @param {string} params.fileName - 文件名
   * @param {string} params.fileType - 文件类型
   * @param {string} params.fileId - 文件ID（可选）
   * @param {string} params.cloudPath - 云路径（可选）
   * @param {string} params.userId - 用户ID（可选）
   * @returns {Promise<Object>} 任务提交结果
   */
  async submitTask(params) {
    try {
      console.log('📤 提交简历解析任务...', params);

      const result = await wx.cloud.callFunction({
        name: 'resumeTaskSubmitter',
        data: params
      });

      if (result.result && result.result.success) {
        const taskData = result.result.data;
        console.log('✅ 任务提交成功:', taskData.taskId);
        return {
          success: true,
          taskId: taskData.taskId,
          status: taskData.status,
          message: taskData.message,
          estimatedTime: taskData.estimatedTime
        };
      } else {
        throw new Error(result.result?.error || '任务提交失败');
      }
    } catch (error) {
      console.error('❌ 提交任务失败:', error);
      throw new Error(`任务提交失败: ${error.message}`);
    }
  }

  /**
   * 查询任务状态
   * @param {string} taskId - 任务ID
   * @param {string} userId - 用户ID（可选）
   * @returns {Promise<Object>} 任务状态
   */
  async queryTask(taskId, userId = null) {
    try {
      const result = await wx.cloud.callFunction({
        name: 'resumeTaskQuery',
        data: { taskId, userId }
      });

      if (result.result && result.result.success) {
        return result.result.data;
      } else {
        throw new Error(result.result?.error || '查询任务失败');
      }
    } catch (error) {
      console.error('❌ 查询任务失败:', error);
      throw new Error(`查询任务失败: ${error.message}`);
    }
  }

  /**
   * 轮询任务直到完成
   * @param {string} taskId - 任务ID
   * @param {string} userId - 用户ID（可选）
   * @param {Function} onProgress - 进度回调函数
   * @returns {Promise<Object>} 最终结果
   */
  async pollTaskUntilComplete(taskId, userId = null, onProgress = null) {
    const startTime = Date.now();
    let attempts = 0;
    const maxAttempts = Math.floor(this.maxPollingTime / this.pollingInterval);

    console.log(`🔄 开始轮询任务状态: ${taskId}`);

    while (attempts < maxAttempts) {
      try {
        const taskStatus = await this.queryTask(taskId, userId);
        attempts++;

        console.log(`🔍 轮询第${attempts}次, 状态: ${taskStatus.status}`);

        // 调用进度回调
        if (onProgress && typeof onProgress === 'function') {
          onProgress(taskStatus);
        }

        // 检查任务状态
        switch (taskStatus.status) {
          case 'completed':
            console.log('✅ 任务完成!');
            return {
              success: true,
              data: taskStatus.result?.data,
              processingTime: taskStatus.processingTime,
              totalTime: Date.now() - startTime
            };

          case 'failed':
            console.error('❌ 任务失败:', taskStatus.error);
            throw new Error(`任务处理失败: ${taskStatus.error}`);

          case 'pending':
          case 'processing':
            // 继续轮询
            console.log(`⏳ 任务${taskStatus.status}, 继续等待...`);
            if (taskStatus.progress) {
              console.log(`📋 进度: ${taskStatus.progress}`);
            }
            break;

          default:
            console.warn('⚠️ 未知任务状态:', taskStatus.status);
        }

        // 等待下次轮询
        await new Promise(resolve => setTimeout(resolve, this.pollingInterval));

      } catch (error) {
        console.error(`❌ 轮询第${attempts}次失败:`, error);

        // 如果是网络错误，继续重试
        if (error.message.includes('网络') || error.message.includes('timeout')) {
          await new Promise(resolve => setTimeout(resolve, this.pollingInterval));
          continue;
        }

        // 其他错误直接抛出
        throw error;
      }
    }

    // 超时
    throw new Error(`任务轮询超时 (${this.maxPollingTime / 1000}秒), 请稍后手动查询任务状态`);
  }

  /**
   * 一键解析简历（提交+轮询）
   * @param {Object} params - 简历参数
   * @param {Function} onProgress - 进度回调
   * @returns {Promise<Object>} 解析结果
   */
  async parseResume(params, onProgress = null) {
    try {
      // 步骤1: 提交任务
      console.log('🚀 开始异步简历解析...');
      const submitResult = await this.submitTask(params);

      // 步骤2: 轮询直到完成
      const result = await this.pollTaskUntilComplete(
        submitResult.taskId,
        params.userId,
        onProgress
      );

      console.log('🎉 简历解析完成!');
      return result;

    } catch (error) {
      console.error('❌ 简历解析失败:', error);
      throw error;
    }
  }

  /**
   * 设置轮询参数
   * @param {number} interval - 轮询间隔（毫秒）
   * @param {number} maxTime - 最大轮询时间（毫秒）
   */
  setPollingConfig(interval, maxTime) {
    this.pollingInterval = interval;
    this.maxPollingTime = maxTime;
    console.log(`⚙️ 轮询配置已更新: 间隔${interval}ms, 最大时间${maxTime}ms`);
  }
}

// 创建全局实例
const asyncResumeAPI = new AsyncResumeAPI();

// 🔧 修复：确保在微信小程序环境中正确注册全局变量
if (typeof global !== 'undefined') {
  global.AsyncResumeAPI = AsyncResumeAPI;
  global.asyncResumeAPI = asyncResumeAPI;
}

// 导出（Node.js环境）
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AsyncResumeAPI;
}

/**
 * 使用示例:
 * 
 * // 方式1: 一键解析（推荐）
 * const result = await asyncResumeAPI.parseResume({
 *   resumeContent: '简历内容...',
 *   fileName: 'resume.pdf',
 *   fileType: 'pdf',
 *   userId: 'user123'
 * }, (progress) => {
 *   console.log('进度更新:', progress.status, progress.progress);
 * });
 * 
 * // 方式2: 分步操作
 * const submitResult = await asyncResumeAPI.submitTask({...});
 * const finalResult = await asyncResumeAPI.pollTaskUntilComplete(submitResult.taskId);
 * 
 * // 方式3: 手动查询
 * const status = await asyncResumeAPI.queryTask(taskId);
 */
