/**
 * Resume Task Submitter - 简历解析任务提交函数
 * 🎯 核心功能：快速接收任务请求，写入数据库，立即返回taskId
 * 🚀 异步架构：不调用其他云函数，避免15秒超时问题
 * 📋 使用方式：前端收到taskId后，可主动调用resumeWorker或通过其他方式触发处理
 */

'use strict';

const cloud = require('wx-server-sdk');

// 初始化云开发环境
cloud.init({
  env: 'zemuresume-4gjvx1wea78e3d1e'
});

const db = cloud.database();

/**
 * 主处理函数
 */
exports.main = async (event, context) => {
  const startTime = Date.now();
  console.log('🚀 ResumeTaskSubmitter 开始处理请求', { event });

  try {
    // 解析输入数据
    let requestData = {};
    if (typeof event === 'string') {
      requestData = JSON.parse(event);
    } else if (event.body) {
      requestData = typeof event.body === 'string' ? JSON.parse(event.body) : event.body;
    } else {
      requestData = event;
    }

    const { resumeContent, fileName, fileType, fileId, cloudPath, userId } = requestData;

    // 验证必要参数
    if (!resumeContent && !fileId && !cloudPath) {
      return createErrorResponse('缺少简历内容参数 (resumeContent 或 fileId/cloudPath)');
    }

    // 生成任务ID
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    // 创建任务记录
    const taskData = {
      taskId,
      status: 'pending', // pending, processing, completed, failed
      userId: userId || 'anonymous',
      input: {
        resumeContent,
        fileName,
        fileType,
        fileId,
        cloudPath
      },
      result: null,
      error: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      startedAt: null,
      completedAt: null,
      processingTime: null
    };

    // 写入数据库
    console.log('📝 写入任务到数据库:', taskId);
    await db.collection('resumeTasks').add({
      data: taskData
    });

    // 🚀 立即触发resumeWorker进行异步处理（修复关键步骤）
    console.log('✅ 任务已写入数据库，立即触发resumeWorker处理');

    try {
      // 异步触发resumeWorker，不等待结果
      cloud.callFunction({
        name: 'resumeWorker',
        data: {
          taskId,
          fileId: requestData.fileId,
          fileName: requestData.fileName,
          fileType: requestData.fileType,
          resumeContent: requestData.resumeContent, // 🔧 修复：传递简历内容
          cloudPath: requestData.cloudPath,
          triggerMode: 'auto_server_trigger'
        }
      }).then(() => {
        console.log('✅ resumeWorker自动触发成功');
      }).catch(error => {
        console.warn('⚠️ resumeWorker自动触发失败，但任务已创建:', error);
      });
    } catch (error) {
      console.warn('⚠️ resumeWorker触发异常，但任务已创建:', error);
    }

    console.log('🔄 resumeWorker已触发，前端立即收到响应');

    const processingTime = Date.now() - startTime;
    console.log('✅ 任务提交成功', { taskId, processingTime });

    return createSuccessResponse({
      success: true,
      taskId,
      status: 'pending',
      message: '任务已提交，请使用taskId轮询结果',
      estimatedTime: '30-60秒',
      processingTime,
      // 🎯 异步架构说明
      asyncInfo: {
        description: '任务已写入数据库，可通过以下方式触发处理：',
        triggerMethods: [
          '1. 前端主动调用resumeWorker云函数',
          '2. 使用taskId参数调用resumeWorker',
          '3. 通过数据库触发器自动处理'
        ],
        queryMethod: '使用resumeTaskQuery云函数查询处理状态'
      }
    });

  } catch (error) {
    const processingTime = Date.now() - startTime;
    console.error('❌ 任务提交失败:', error);

    return createErrorResponse(error.message, {
      processingTime,
      timestamp: new Date().toISOString()
    });
  }
};

/**
 * 创建成功响应
 */
function createSuccessResponse(data, metadata = {}) {
  return {
    statusCode: 200,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: true,
      data: data,
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e',
        ...metadata
      }
    })
  };
}

/**
 * 创建错误响应
 */
function createErrorResponse(message, metadata = {}) {
  return {
    statusCode: 500,
    headers: {
      'Content-Type': 'application/json',
      'Access-Control-Allow-Origin': '*'
    },
    body: JSON.stringify({
      success: false,
      error: message,
      metadata: {
        timestamp: new Date().toISOString(),
        environment: 'zemuresume-4gjvx1wea78e3d1e',
        ...metadata
      }
    })
  };
}
